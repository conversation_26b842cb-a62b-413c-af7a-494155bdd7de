#!/usr/bin/env python3
"""
Test popular recipe functionality
"""

import requests
import json
import time

def test_popular_recipes_api():
    """Test the popular recipes API endpoint"""
    
    base_url = 'http://127.0.0.1:5000'
    
    print("🍽️  Testing Popular Recipes API...")
    print("=" * 60)
    
    try:
        # Wait for server to be ready
        time.sleep(2)
        
        # Test prescriptive analytics endpoint (contains popular recipes)
        response = requests.get(f"{base_url}/api/analytics/prescriptive", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'success':
                analytics_data = data.get('data', {})
                popular_recipes = analytics_data.get('popular_recipes', [])
                
                print(f"✅ API Response Status: {data.get('status')}")
                print(f"✅ Popular Recipes Count: {len(popular_recipes)}")
                
                if len(popular_recipes) > 0:
                    print("\n📋 Popular Recipe Details:")
                    for i, recipe in enumerate(popular_recipes[:3]):  # Show first 3
                        print(f"  {i+1}. {recipe.get('name', 'Unknown')}")
                        print(f"     Rating: {recipe.get('avg_rating', 0):.1f}/5")
                        print(f"     Reviews: {recipe.get('review_count', 0)}")
                        print(f"     Description: {recipe.get('description', 'No description')[:80]}...")
                        print()
                    
                    # Check if recipes have proper data structure
                    first_recipe = popular_recipes[0]
                    required_fields = ['id', 'name', 'ingredients', 'avg_rating', 'review_count']
                    missing_fields = [field for field in required_fields if field not in first_recipe]
                    
                    if missing_fields:
                        print(f"⚠️  Missing fields in recipe data: {missing_fields}")
                        return False
                    else:
                        print("✅ Recipe data structure is complete")
                        return True
                else:
                    print("⚠️  No popular recipes found")
                    print("   This might be expected if no user reviews exist yet")
                    
                    # Check debug info
                    debug_info = data.get('debug', {})
                    print(f"\n🔍 Debug Info:")
                    print(f"   MongoDB Available: {debug_info.get('mongo_available', False)}")
                    print(f"   Recommender Available: {debug_info.get('recommender_available', False)}")
                    print(f"   Popular Recipes Count: {debug_info.get('popular_recipes_count', 0)}")
                    
                    return True  # This is acceptable if no reviews exist
            else:
                print(f"❌ API returned error status: {data.get('status')}")
                print(f"   Message: {data.get('message', 'No message')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def test_dashboard_popular_recipes():
    """Test that dashboard page loads popular recipes correctly"""
    
    base_url = 'http://127.0.0.1:5000'
    
    print("\n🖥️  Testing Dashboard Popular Recipes Display...")
    print("=" * 60)
    
    try:
        response = requests.get(f"{base_url}/dashboard", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for popular recipes section
            if 'popularRecipes' in content:
                print("✅ Dashboard contains popular recipes section")
                
                # Check for Vue.js data binding
                if '${' in content and 'popularRecipes' in content:
                    print("✅ Vue.js data binding for popular recipes found")
                    return True
                else:
                    print("⚠️  Vue.js data binding might be missing")
                    return False
            else:
                print("❌ Dashboard missing popular recipes section")
                return False
        else:
            print(f"❌ Dashboard page error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard test error: {e}")
        return False

def main():
    """Main function"""
    print("============================================================")
    print("🧪 TESTING POPULAR RECIPE FUNCTIONALITY")
    print("============================================================")
    
    # Test API
    api_test_passed = test_popular_recipes_api()
    
    # Test Dashboard
    dashboard_test_passed = test_dashboard_popular_recipes()
    
    print("\n" + "=" * 60)
    print("📊 POPULAR RECIPES TEST SUMMARY")
    print("=" * 60)
    
    if api_test_passed and dashboard_test_passed:
        print("🎉 POPULAR RECIPES FUNCTIONALITY WORKING!")
        print("✅ API endpoint responds correctly")
        print("✅ Dashboard displays popular recipes section")
        print("✅ Data structure is complete")
        return True
    else:
        if not api_test_passed:
            print("❌ API endpoint issues found")
        if not dashboard_test_passed:
            print("❌ Dashboard display issues found")
        print("⚠️  Popular recipes functionality needs attention")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
