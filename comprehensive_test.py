#!/usr/bin/env python3
"""
Comprehensive test of all SisaRasa functionality
"""

import requests
import time
import json

def test_all_pages():
    """Test all main pages"""
    
    base_url = 'http://127.0.0.1:5000'
    
    pages = [
        ('/', 'Home Page'),
        ('/welcome', 'Welcome Page'),
        ('/login', 'Login Page'),
        ('/signup', 'Signup Page'),
        ('/dashboard', 'Dashboard Page'),
        ('/profile', 'Profile Page'),
        ('/save-recipe', 'Save Recipe Page'),
        ('/share-recipe', 'Share Recipe Page'),
        ('/community-recipes', 'Community Recipes Page'),
        ('/search-results', 'Search Results Page')
    ]
    
    print("🌐 Testing All Pages...")
    print("=" * 60)
    
    results = {}
    
    for path, name in pages:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            
            if response.status_code == 200:
                content = response.text
                
                # Check for template errors
                if 'templatesyntaxerror' in content.lower():
                    results[name] = "❌ TEMPLATE ERROR"
                elif '${url_for(' in content:
                    results[name] = "❌ URL_FOR NOT PROCESSED"
                elif len(content) < 500:
                    results[name] = f"⚠️  SHORT ({len(content)} chars)"
                else:
                    results[name] = "✅ OK"
            else:
                results[name] = f"❌ HTTP {response.status_code}"
                
        except Exception as e:
            results[name] = f"❌ ERROR: {str(e)[:30]}"
    
    # Print results
    for name, result in results.items():
        print(f"{name:25} {result}")
    
    success_count = sum(1 for result in results.values() if result.startswith('✅'))
    total_count = len(results)
    
    print(f"\n📊 Pages: {success_count}/{total_count} working")
    return success_count == total_count

def test_api_endpoints():
    """Test critical API endpoints"""
    
    base_url = 'http://127.0.0.1:5000'
    
    endpoints = [
        ('/api/analytics/prescriptive', 'Prescriptive Analytics'),
        ('/api/analytics/personal', 'Personal Analytics'),
        ('/api/test-debug', 'Debug Test')
    ]
    
    print("\n🔌 Testing API Endpoints...")
    print("=" * 60)
    
    results = {}
    
    for path, name in endpoints:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('status') == 'success':
                        results[name] = "✅ OK"
                    else:
                        results[name] = f"⚠️  STATUS: {data.get('status', 'unknown')}"
                except:
                    results[name] = "⚠️  NOT JSON"
            else:
                results[name] = f"❌ HTTP {response.status_code}"
                
        except Exception as e:
            results[name] = f"❌ ERROR: {str(e)[:30]}"
    
    # Print results
    for name, result in results.items():
        print(f"{name:25} {result}")
    
    success_count = sum(1 for result in results.values() if result.startswith('✅'))
    total_count = len(results)
    
    print(f"\n📊 APIs: {success_count}/{total_count} working")
    return success_count == total_count

def test_static_files():
    """Test static file accessibility"""
    
    base_url = 'http://127.0.0.1:5000'
    
    static_files = [
        ('/static/images/logo.png', 'Logo Image'),
        ('/static/images/user.png', 'User Image'),
        ('/static/main.js', 'Main JavaScript')
    ]
    
    print("\n🖼️  Testing Static Files...")
    print("=" * 60)
    
    results = {}
    
    for path, name in static_files:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            
            if response.status_code == 200:
                size = len(response.content)
                if size > 0:
                    results[name] = f"✅ OK ({size} bytes)"
                else:
                    results[name] = "⚠️  EMPTY FILE"
            else:
                results[name] = f"❌ HTTP {response.status_code}"
                
        except Exception as e:
            results[name] = f"❌ ERROR: {str(e)[:30]}"
    
    # Print results
    for name, result in results.items():
        print(f"{name:25} {result}")
    
    success_count = sum(1 for result in results.values() if result.startswith('✅'))
    total_count = len(results)
    
    print(f"\n📊 Static Files: {success_count}/{total_count} working")
    return success_count == total_count

def test_popular_recipes():
    """Test popular recipe functionality"""
    
    base_url = 'http://127.0.0.1:5000'
    
    print("\n🍽️  Testing Popular Recipes...")
    print("=" * 60)
    
    try:
        response = requests.get(f"{base_url}/api/analytics/prescriptive", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'success':
                popular_recipes = data.get('data', {}).get('popular_recipes', [])
                
                if len(popular_recipes) > 0:
                    print(f"✅ Found {len(popular_recipes)} popular recipes")
                    
                    # Check first recipe structure
                    first_recipe = popular_recipes[0]
                    required_fields = ['id', 'name', 'ingredients', 'avg_rating']
                    missing_fields = [field for field in required_fields if field not in first_recipe]
                    
                    if missing_fields:
                        print(f"⚠️  Missing fields: {missing_fields}")
                        return False
                    else:
                        print("✅ Recipe data structure complete")
                        return True
                else:
                    print("⚠️  No popular recipes found (acceptable if no reviews)")
                    return True
            else:
                print(f"❌ API error: {data.get('message', 'Unknown')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main comprehensive test"""
    print("============================================================")
    print("🧪 COMPREHENSIVE SISARASA FUNCTIONALITY TEST")
    print("============================================================")
    
    # Wait for server
    print("⏳ Waiting for server...")
    time.sleep(2)
    
    # Run all tests
    pages_ok = test_all_pages()
    apis_ok = test_api_endpoints()
    static_ok = test_static_files()
    popular_ok = test_popular_recipes()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL COMPREHENSIVE TEST RESULTS")
    print("=" * 60)
    
    all_tests = [
        ("Pages", pages_ok),
        ("APIs", apis_ok),
        ("Static Files", static_ok),
        ("Popular Recipes", popular_ok)
    ]
    
    for test_name, result in all_tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    overall_success = all(result for _, result in all_tests)
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 ALL TESTS PASSED! SISARASA IS FULLY FUNCTIONAL!")
        print("✅ Navigation working")
        print("✅ Logo display fixed")
        print("✅ Popular recipes working")
        print("✅ All pages accessible")
        print("✅ APIs responding correctly")
    else:
        print("⚠️  SOME TESTS FAILED - MANUAL REVIEW NEEDED")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
