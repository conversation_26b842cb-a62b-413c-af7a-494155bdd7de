#!/usr/bin/env python3
"""
Test navigation fix by checking URL generation
"""

import requests
import sys
import os
import time

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

def test_navigation_urls():
    """Test that navigation URLs are generated correctly"""
    
    base_url = 'http://127.0.0.1:5000'
    
    print("🔗 Testing navigation URL generation...")
    print("=" * 60)
    
    # Test dashboard page to see if URLs are generated correctly
    try:
        response = requests.get(f"{base_url}/dashboard", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check if URLs are properly generated (not literal ${url_for...})
            if '${url_for(' in content:
                print("❌ PROBLEM: Found literal ${url_for(...)} in dashboard content")
                print("   This means Jinja2 template functions are not being processed")
                
                # Find specific instances
                import re
                matches = re.findall(r'\$\{url_for\([^}]+\)\}', content)
                for match in matches[:5]:  # Show first 5 matches
                    print(f"   Found: {match}")
                
                return False
            else:
                print("✅ Dashboard URLs are properly generated")
                
                # Check for proper navigation links
                expected_links = ['/dashboard', '/profile', '/save-recipe', '/share-recipe', '/community-recipes']
                found_links = []
                
                for link in expected_links:
                    if f'href="{link}"' in content:
                        found_links.append(link)
                        print(f"✅ Found navigation link: {link}")
                    else:
                        print(f"❌ Missing navigation link: {link}")
                
                if len(found_links) == len(expected_links):
                    print("✅ All navigation links found!")
                    return True
                else:
                    print(f"⚠️  Found {len(found_links)}/{len(expected_links)} navigation links")
                    return False
        else:
            print(f"❌ Dashboard page returned status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_individual_pages():
    """Test that individual pages are accessible"""
    
    base_url = 'http://127.0.0.1:5000'
    
    pages_to_test = [
        ('/dashboard', 'Dashboard'),
        ('/profile', 'Profile'),
        ('/save-recipe', 'Save Recipe'),
        ('/share-recipe', 'Share Recipe'),
        ('/community-recipes', 'Community Recipes'),
        ('/search-results', 'Search Results')
    ]
    
    print("\n🌐 Testing individual page accessibility...")
    print("=" * 60)
    
    results = {}
    
    for path, name in pages_to_test:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            
            if response.status_code == 200:
                # Quick check for template errors
                content = response.text.lower()
                if 'templatesyntaxerror' in content or 'jinja2.exceptions' in content:
                    results[name] = "❌ TEMPLATE ERROR"
                elif len(response.text) < 1000:  # Suspiciously short
                    results[name] = f"⚠️  SHORT RESPONSE ({len(response.text)} chars)"
                else:
                    results[name] = "✅ OK"
            else:
                results[name] = f"❌ HTTP {response.status_code}"
                
        except Exception as e:
            results[name] = f"❌ ERROR: {str(e)[:50]}"
    
    # Print results
    for name, result in results.items():
        print(f"{name:20} {result}")
    
    success_count = sum(1 for result in results.values() if result.startswith('✅'))
    total_count = len(results)
    
    return success_count == total_count

def main():
    """Main function"""
    print("============================================================")
    print("🧪 TESTING NAVIGATION FIX")
    print("============================================================")
    
    # Wait a moment for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(3)
    
    # Test URL generation
    url_test_passed = test_navigation_urls()
    
    # Test page accessibility
    page_test_passed = test_individual_pages()
    
    print("\n" + "=" * 60)
    print("📊 NAVIGATION TEST SUMMARY")
    print("=" * 60)
    
    if url_test_passed and page_test_passed:
        print("🎉 ALL NAVIGATION TESTS PASSED!")
        print("✅ URLs are properly generated")
        print("✅ All pages are accessible")
        print("✅ Navigation should work correctly")
        return True
    else:
        if not url_test_passed:
            print("❌ URL generation issues found")
        if not page_test_passed:
            print("❌ Page accessibility issues found")
        print("⚠️  Navigation fix needs more work")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
